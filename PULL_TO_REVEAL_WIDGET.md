# Pull-to-Reveal Widget

## Overview

The Pull-to-Reveal Widget is an interactive UI component that provides quick access to portfolio information through an intuitive pull gesture. It's designed to enhance user engagement while maintaining a clean, minimal interface.

## Features

### 🎯 Core Functionality
- **Pull Gesture Detection**: Responds to both mouse drag and touch gestures
- **Threshold-based Reveal**: Widget opens when pulled beyond a configurable threshold (default: 120px)
- **Smooth Animations**: Fluid transitions using CSS cubic-bezier curves
- **Snap-back Behavior**: Automatically closes if not pulled far enough
- **Auto-hide Hint**: Visual hint disappears after 5 seconds or first interaction

### 🎨 Visual Design
- **Theme Compatibility**: Supports both light and dark themes
- **Backdrop Blur**: Modern glassmorphism effect for the revealed content
- **Subtle Animations**: Gentle pull hint animation to indicate interactivity
- **Responsive Design**: Works seamlessly on desktop and mobile devices

### 📱 Content Sections
- **Quick Bio**: Brief professional summary
- **Contact Information**: Email, GitHub, LinkedIn links with icons
- **Quick Actions**: Download resume and view projects buttons
- **Status Indicator**: Shows availability for opportunities

## Technical Implementation

### Component Structure
```
PullToRevealWidget.tsx (SolidJS Component)
├── State Management (SolidJS signals)
├── Event Handlers (Mouse & Touch)
├── Animation Logic
└── Content Rendering
```

### Key Technologies
- **SolidJS**: For reactive state management and component logic
- **Tailwind CSS**: For styling and responsive design
- **Custom CSS Animations**: For smooth pull transitions
- **Astro Integration**: Client-only rendering for browser APIs

### Configuration Options
```typescript
interface PullToRevealWidgetProps {
  threshold?: number;        // Pull distance to trigger reveal (default: 120px)
  maxPullDistance?: number;  // Maximum pull distance (default: 300px)
}
```

## Usage

### Integration
The widget is integrated into the main portfolio page (`src/pages/index.astro`) with client-only rendering:

```astro
<PullToRevealWidget client:only="solid-js" />
```

### Customization
- **Content**: Modify the content sections in the component
- **Styling**: Adjust Tailwind classes or add custom CSS
- **Behavior**: Change threshold and maxPullDistance props
- **Animation**: Modify CSS animations in `global.css`

## Browser Compatibility

- **Desktop**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile**: iOS Safari, Chrome Mobile, Samsung Internet
- **Touch Support**: Full touch gesture support for mobile devices
- **Accessibility**: Keyboard navigation and screen reader friendly

## Performance Considerations

- **Client-only Rendering**: Prevents SSR issues with browser APIs
- **Event Delegation**: Efficient event handling with global listeners
- **Smooth Animations**: Hardware-accelerated CSS transforms
- **Memory Management**: Proper cleanup of event listeners

## Future Enhancements

- **Keyboard Navigation**: Arrow key support for accessibility
- **Gesture Customization**: Configurable pull directions
- **Content Templates**: Multiple content layout options
- **Analytics Integration**: Track widget interaction metrics
