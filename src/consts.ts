import type { Site, Page, <PERSON>s, Socials } from "@types";

// Global
export const SITE: Site = {
  TITLE: "Imad IDALI OUALI",
  DESCRIPTION: "A portfolio website showcasing my skills and projects.",
  AUTHOR: "Imad IDALI OUALI",
};

// Experience Page
export const EXPERIENCE: Page = {
  TITLE: "Experience",
  DESCRIPTION: "Places I have worked.",
};

// Talks Page
export const TALKS: Page = {
  TITLE: "Talks",
  DESCRIPTION: "Talks I have given.",
};

// Projects Page
export const PROJECTS: Page = {
  TITLE: "Projects",
  DESCRIPTION: "Recent projects I have worked on.",
};

// Search Page
export const SEARCH: Page = {
  TITLE: "Search",
  DESCRIPTION: "Search all posts and projects by keyword.",
};

// Links
export const LINKS: Links = [
  {
    TEXT: "Home",
    HREF: "/",
  },
  {
    TEXT: "Projects",
    HREF: "/projects",
  },
  {
    TEXT: "Talks",
    HREF: "/talks",
  },
];

// Socials
export const SOCIALS: Socials = [
  {
    NAME: "Email",
    ICON: "email",
    TEXT: "<EMAIL>",
    HREF: "mailto:<EMAIL>",
  },
  {
    NAME: "Github",
    ICON: "github",
    TEXT: "imadidaliouali",
    HREF: "https://github.com/imadidaliouali",
  },
  {
    NAME: "LinkedIn",
    ICON: "linkedin",
    TEXT: "imadidaliouali",
    HREF: "https://www.linkedin.com/in/imadidaliouali",
  },
  {
    NAME: "Twitter",
    ICON: "twitter-x",
    TEXT: "imad_idaliouali",
    HREF: "https://x.com/imad_idaliouali",
  },
];
