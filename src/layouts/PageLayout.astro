---
import "@styles/global.css"
import BaseHead from "@components/BaseHead.astro"
import Header from "@components/Header.astro"
import Footer from "@components/Footer.astro"
import Drawer from "@components/Drawer.astro"
import CustomCursor from "@components/CustomCursor.astro"
const { title, description } = Astro.props
import { SITE } from "@consts"
---

<!doctype html>
<html lang="en">
  <head>
    <BaseHead title={`${title} | ${SITE.TITLE}`} description={description} />
  </head>
  <body>
    <CustomCursor />
    <Header />
    <Drawer />
    <main>
      <slot />
    </main>
    <Footer />
  </body>
</html>
