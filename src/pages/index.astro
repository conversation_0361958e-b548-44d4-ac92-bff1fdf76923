---
import { getCollection } from "astro:content";
import PageLayout from "@layouts/PageLayout.astro";
import ArrowCard from "@components/ArrowCard";
import StackCard from "@components/StackCard.astro";
import { SITE, SOCIALS } from "@consts";
import TwinklingStars from "@components/TwinklingStars.astro";
import MeteorShower from "@components/MeteorShower.astro";
import MouseScroll from "@components/MouseScroll.astro";

const experience = [
  {
    text: "Full Stack Developer",
    company: "Youzin",
    date: "Jul 2024 - Aug 2024",
    description: "Designed and developed a responsive website for Olympique Dcheira to simplify the subscription card reservation process, implemented secure payment gateways, and enhanced user experience with intuitive interfaces.",
    link: "https://www.linkedin.com/company/youzinsarl",
  },
  {
    text: "Mobile Developer",
    company: "IMOUMEDIA24",
    date: "April 2023 - June 2023",
    description: "Enhanced the ImouMedia24 mobile application using React Native, introduced advanced search functionality and customizable user interfaces, significantly improving usability and user engagement.",
    link: "https://imoumedia24.com",
  },
  {
    text: "Mobile Developer",
    company: "IMOUMEDIA24",
    date: "June 2022 - July 2022",
    description: "Developed a news-focused mobile application using Flutter, implemented a robust cache management system to optimize performance and ensure faster load times.",
    link: "https://imoumedia24.com",
  },
];

const projects = (await getCollection("projects"))
  .filter((project) => !project.data.draft)
  .sort((a, b) => b.data.date.getTime() - a.data.date.getTime())
  .slice(0, 3);

const education = [
  {
    text: "Ecole Normale Supérieure de l'Enseignement Technique Mohammedia",
    date: "Sep. 2023 - Present",
    degree: "Software Engineering and Distributed Systems",
    link: "http://www.enset-media.ac.ma",
  },
  {
    text: "Ecole Supérieure de Technologie Essaouira",
    date: "Sep. 2021 - June 2023",
    degree: "University Diploma in Computer Engineering",
    link: "https://www.uca.ma/este/fr",
  },
  {
    text: "Lycée Qualifiant Argane Tamanar Essaouira",
    date: "June 2021",
    degree: "Baccalaureate in Physical Sciences",
  },
];

const skills = [
  {
    text: "Next.js",
    icon: "nextjs",
    href: "https://nextjs.org",
  },
  {
    text: "React",
    icon: "react",
    href: "https://reactjs.org",
  },
  {
    text: "Node.js",
    icon: "nodejs",
    href: "https://nodejs.org",
  },
  {
    text: "Express.js",
    icon: "express",
    href: "https://expressjs.com",
  },
  {
    text: "JavaScript",
    icon: "javascript",
    href: "https://www.javascript.com",
  },
  {
    text: "TypeScript",
    icon: "typescript",
    href: "https://www.typescriptlang.org",
  },
  {
    text: "Tailwind CSS",
    icon: "tailwindcss",
    href: "https://tailwindcss.com",
  },
  {
    text: "MongoDB",
    icon: "mongodb",
    href: "https://www.mongodb.com",
  },
  {
    text: "Flutter",
    icon: "flutter",
    href: "https://flutter.dev",
  },
  {
    text: "Java",
    icon: "java",
    href: "https://www.java.com",
  },
  {
    text: "C++",
    icon: "c++",
    href: "https://isocpp.org",
  },
  {
    text: "PHP",
    icon: "php",
    href: "https://www.php.net",
  },
  {
    text: "Prisma",
    icon: "prisma",
    href: "https://www.prisma.io",
  },
  {
    text: "Redis",
    icon: "redis",
    href: "https://redis.io",
  },
  {
    text: "MySQL",
    icon: "mysql",
    href: "https://www.mysql.com",
  },
  {
    text: "Firebase",
    icon: "firebase",
    href: "https://firebase.google.com",
  },
  {
    text: "Docker",
    icon: "docker",
    href: "https://www.docker.com",
  },
  {
    text: "Git",
    icon: "git",
    href: "https://git-scm.com",
  },
  {
    text: "Linux",
    icon: "linux",
    href: "https://www.linux.org",
  },
];
---

<PageLayout title="Home" description={SITE.DESCRIPTION}>
  <!-- Light Mode: Particles -->
  <div class="absolute inset-0 block dark:hidden">
    <div id="particles1" class="fixed inset-0"></div>
    <div id="particles2" class="fixed inset-0"></div>
    <div id="particles3" class="fixed inset-0"></div>
  </div>

  <!-- Dark Theme: Stars -->
  <div class="absolute inset-0 bg-black hidden dark:block">
    <div id="stars1" class="fixed inset-0"></div>
    <div id="stars2" class="fixed inset-0"></div>
    <div id="stars3" class="fixed inset-0"></div>
  </div>

  <!-- Dark Theme: Twinkling Stars / Metors -->
  <div id="galaxy" class="fixed inset-0">
    <div class="hidden dark:block">
      <TwinklingStars />
      <MeteorShower />
    </div>
  </div>

  <script is:inline src="/js/bg.js"></script>

  <!-- HERO -->
  <section class="relative h-screen w-full">
    <div
      id="planetcont"
      class="animate absolute inset-0 top-1/4 overflow-hidden"
    >
      <div
        id="crescent"
        class="absolute top-0 left-1/2 -translate-x-1/2 w-[250vw] min-h-[100vh] aspect-square rounded-full p-[1px] bg-gradient-to-b from-black/25 dark:from-white/75 from-0% to-transparent to-5%"
      >
        <div
          id="planet"
          class="w-full h-full bg-white dark:bg-black rounded-full p-[1px] overflow-hidden flex justify-center"
        >
          <div
            id="blur"
            class="w-full h-20 rounded-full bg-neutral-900/25 dark:bg-white/25 blur-3xl"
          >
          </div>
        </div>
      </div>
    </div>
    <div
      class="animate absolute h-full w-full flex items-center justify-center"
    >
      <div class="relative w-full h-full flex items-center justify-center">
        <div class="p-5 text-center">
          <div
            class="relative mx-auto w-32 h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 mb-4"
          >
            <img
              src="/picture.jpg"
              alt="Imad IDALI OUALI"
              class="rounded-full object-cover w-full h-full border-4 border-black/10 dark:border-white/10 shadow-lg"
            />
            <div
              class="absolute inset-0 rounded-full bg-gradient-to-tr from-black/5 to-transparent dark:from-white/5 pointer-events-none"
            >
            </div>
          </div>
          <p
            class="animated text-lg md:text-xl lg:text-2xl font-semibold opacity-75"
          >
            Hello, I am ...
          </p>
          <p
            class="animated text-2xl md:text-3xl lg:text-4xl font-bold uppercase text-black dark:text-white"
          >
            Imad IDALI OUALI
          </p>
          <p class="animated text-sm md:text-base lg:text-lg opacity-75">
            Software Engineering and Distributed Systems Student
          </p>
          <div
            id="ctaButtons"
            class="animated flex flex-wrap gap-4 justify-center mt-5"
          >
            <button
              id="letstalk"
              class="py-2 px-4 rounded truncate text-xs md:text-sm lg:text-base bg-black dark:bg-white text-white dark:text-black hover:opacity-75 blend"
              aria-label="Let's Talk"
            >
              Let's Talk
            </button>
            <a
              href="/projects"
              class="py-2 px-4 truncate rounded text-xs md:text-sm lg:text-base border border-black/25 dark:border-white/25 hover:bg-black/5 hover:dark:bg-white/15 blend"
              aria-label="View projects"
            >
              View projects
            </a>
          </div>
        </div>
      </div>
    </div>
    <MouseScroll />
  </section>

  <div class="relative bg-white dark:bg-black">
    <div class="mx-auto max-w-screen-sm p-5 space-y-16 pb-16">
      <!-- About Section -->
      <section id="about" class="animate">
        <div class="space-y-4">
          <p class="font-semibold text-black dark:text-white">About Me</p>
          <article>
            <p>I am a <b><i>software engineer</i></b>, <b><i>mobile developer</i></b>, <b><i>full stack developer</i></b>, <b><i>student</i></b>, and <b><i>tech enthusiast</i></b>.</p>
            <p>
              I love building user-friendly applications and solving complex
              problems. My passion lies in creating seamless user experiences
              and optimizing application performance.
            </p>
            <p>
              During my career, I have developed websites and mobile
              applications, optimized application performance using various
              technologies.
            </p>
            <p>
              Now, I am pursuing my engineering degree in <b
                ><i>Software Engineering and Distributed Systems</i></b
              > at <b><i>ENSET Mohammedia</i></b>, Morocco.
            </p>
          </article>
        </div>
      </section>

      <!-- Experience Section -->
      <section class="animate">
        <div class="space-y-4">
          <p class="font-semibold text-black dark:text-white">Experience</p>
          <ul class="relative space-y-6 sm:space-y-8 before:absolute before:left-[16px] sm:before:left-[22px] before:h-full before:w-0.5
                    before:bg-gradient-to-b before:from-black/20 before:via-black/10 before:to-black/20
                    dark:before:from-white/20 dark:before:via-white/10 dark:before:to-white/20">
            {experience.map((item, index) => (
              <li class="relative pl-10 sm:pl-14">
                <div class="absolute left-0 top-2 flex items-center justify-center w-8 sm:w-11 h-8 sm:h-11">
                  <div class="absolute w-full h-full rounded-full bg-black/5 dark:bg-white/5 animate-pulse" />
                  <div class="relative w-6 sm:w-7 h-6 sm:h-7 rounded-full border-2 border-black/10 dark:border-white/10
                            bg-white dark:bg-black flex items-center justify-center">
                    {index === 0 ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.75"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-black/50 dark:text-white/50"
                    >
                      <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                    </svg>
                    ) : index === 1 ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                           class="text-black/50 dark:text-white/50">
                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                           class="text-black/50 dark:text-white/50">
                        <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                        <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                      </svg>
                    )}
                  </div>
                </div>
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group block rounded-xl border border-black/10 dark:border-white/10 p-3 sm:p-5"
                >
                  <div class="flex flex-col sm:flex-row items-start justify-between gap-2 sm:gap-4">
                    <div class="space-y-1">
                      <h3 class="font-semibold text-sm sm:text-base text-black dark:text-white group-hover:underline line-clamp-2">
                        {item.company}
                      </h3>
                      <p class="text-xs sm:text-sm font-medium text-black/70 dark:text-white/70">
                        {item.text}
                      </p>
                    </div>
                    <span class="inline-block sm:flex-shrink-0 rounded-full bg-black/5 dark:bg-white/5 px-2 sm:px-2.5 py-1 text-xs font-medium
                               text-black/60 dark:text-white/60 whitespace-nowrap">
                      {item.date}
                    </span>
                  </div>
                  <div class="mt-2 text-sm text-black/60 dark:text-white/60">
                    <p>{item.description}</p>
                  </div>
                </a>
              </li>
            ))}
          </ul>
        </div>
      </section>

      <!-- Project Preview Section -->
      <section class="animate">
        <div class="space-y-4">
          <div class="flex justify-between">
            <p class="font-semibold text-black dark:text-white">
              Recent projects
            </p>
            <a
              href="/projects"
              class="w-fit col-span-3 group flex gap-1 items-center underline decoration-[.5px] decoration-black/25 dark:decoration-white/50 hover:decoration-black dark:hover:decoration-white text-black dark:text-white underline-offset-2 blend"
            >
              <span
                class="text-black/75 dark:text-white/75 group-hover:text-black group-hover:dark:text-white blend"
              >
                All projects
              </span>
            </a>
          </div>
          <ul class="space-y-4">
            {
              projects.map((project) => (
                <li>
                  <ArrowCard entry={project} />
                </li>
              ))
            }
          </ul>
        </div>
      </section>

      <!-- Education Section -->
      <section class="animate">
        <div class="space-y-4">
          <p class="font-semibold text-black dark:text-white">Education</p>
          <ul class="relative space-y-6 sm:space-y-8 before:absolute before:left-[16px] sm:before:left-[22px] before:h-full before:w-0.5
                      before:bg-gradient-to-b before:from-black/20 before:via-black/10 before:to-black/20
                      dark:before:from-white/20 dark:before:via-white/10 dark:before:to-white/20">
            {education.map((item, index) => (
              <li class="relative pl-10 sm:pl-14">
                <div class="absolute left-0 top-2 flex items-center justify-center w-8 sm:w-11 h-8 sm:h-11">
                  <div class="absolute w-full h-full rounded-full bg-black/5 dark:bg-white/5 animate-pulse" />
                  <div class="relative w-6 sm:w-7 h-6 sm:h-7 rounded-full border-2 border-black/10 dark:border-white/10
                            bg-white dark:bg-black flex items-center justify-center">
                    {index === 0 ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.75"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="text-black/50 dark:text-white/50"
                    >
                      <path d="M22 10v6M2 10l10-5 10 5-10 5z"/>
                      <path d="M6 12v5c3 3 9 3 12 0v-5"/>
                    </svg>
                    ) : index === 1 ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                           class="text-black/50 dark:text-white/50">
                        <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"/>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                           class="text-black/50 dark:text-white/50">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                      </svg>
                    )}
                  </div>
                </div>
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  class="group block rounded-xl border border-black/10 dark:border-white/10 p-3 sm:p-5"
                >
                  <div class="flex flex-col sm:flex-row items-start justify-between gap-2 sm:gap-4">
                    <div class="space-y-1">
                      <h3 class="font-semibold text-sm sm:text-base text-black dark:text-white group-hover:underline line-clamp-2">
                        {item.text}
                      </h3>
                      <p class="text-xs sm:text-sm font-medium text-black/70 dark:text-white/70">
                        {item.degree}
                      </p>
                    </div>
                    <span class="inline-block sm:flex-shrink-0 rounded-full bg-black/5 dark:bg-white/5 px-2 sm:px-2.5 py-1 text-xs font-medium
                               text-black/60 dark:text-white/60 whitespace-nowrap">
                      {item.date}
                    </span>
                  </div>
                </a>
              </li>
            ))}
          </ul>
        </div>
      </section>

      <!-- Skills Section -->
      <section class="animate">
        <div class="space-y-4">
          <p class="font-semibold text-black dark:text-white">Skills</p>
          <div class="flex flex-wrap items-center gap-2 mt-5">
            {
              skills.map((item, index) => (
                <StackCard text={item.text} icon={item.icon} href={item.href} />
              ))
            }
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="animate">
        <div>
          <p class="font-semibold text-black dark:text-white">Let's Connect</p>
          <p>Reach out to me via email or on social media.</p>
          <div class="grid grid-cols-4 gap-y-2 mt-4 auto-cols-min">
            {
              SOCIALS.map((social, index) => (
                <Fragment key={index}>
                  <div class="col-span-1 flex items-center gap-2">
                    <div class="w-5 h-5 flex-shrink-0">
                      <svg class="w-full h-full fill-current group-hover:fill-black group-hover:dark:fill-white transition-colors">
                        <use href={`/social.svg#${social.ICON}`} />
                      </svg>
                    </div>
                    <span class="whitespace-nowrap truncate text-sm">
                      {social.NAME}
                    </span>
                  </div>
                  <div class="col-span-3 truncate">
                    <a
                      href={social.HREF}
                      target="_blank"
                      class="w-fit col-span-3 group flex gap-1 items-center underline decoration-[.5px] decoration-black/25 dark:decoration-white/50 hover:decoration-black dark:hover:decoration-white text-black dark:text-white underline-offset-2 blend"
                    >
                      <span class="text-black/75 dark:text-white/75 group-hover:text-black group-hover:dark:text-white blend">
                        {social.TEXT}
                      </span>
                    </a>
                  </div>
                </Fragment>
              ))
            }
          </div>
        </div>
      </section>


    </div>
  </div>
</PageLayout>

<script>
  const letsTalkButton = document.getElementById("letstalk");
  const handleClick = () => {
    const contactSection = document.getElementById("contact");
    contactSection?.scrollIntoView({ behavior: "smooth" });
  };
  letsTalkButton?.addEventListener("click", handleClick);
</script>
