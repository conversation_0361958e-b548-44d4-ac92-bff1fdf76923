@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --copy-btn-margin: 10px;
}

@layer base {
  @font-face {
    font-family: "Atkinson";
    src: url("/fonts/atkinson-regular.woff") format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "Atkinson";
    src: url("/fonts/atkinson-bold.woff") format("woff");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

html {
  overflow-y: scroll;
  color-scheme: light;
  background-color: white;
  font-family: "Atkinson", sans-serif;
}

html.dark {
  color-scheme: dark;
  background-color: black;
}

html,
body {
  @apply h-full w-full antialiased;
  @apply bg-white dark:bg-black;
  @apply text-black/75 dark:text-white/75;
}

/* Custom cursor styling for desktop devices */
@media (min-width: 769px) {
  html {
    cursor: none;
  }

  a,
  button,
  input[type="button"],
  input[type="submit"],
  [role="button"],
  .cursor-pointer {
    cursor: none;
  }
}

body {
  @apply relative flex flex-col;
}

main {
  @apply flex flex-col flex-1 bg-white dark:bg-black;
}

header {
  @apply border-b;
  @apply transition-all duration-300 ease-in-out;
}

header:not(.scrolled) {
  @apply bg-transparent border-transparent;
}

header.scrolled {
  @apply bg-white/75 dark:bg-black/50;
  @apply border-black/10 dark:border-white/25;
  @apply backdrop-blur-sm saturate-200;
}

article {
  @apply prose dark:prose-invert max-w-full;
}

.page-heading {
  @apply font-semibold text-black dark:text-white;
}

.blend {
  @apply transition-all duration-300 ease-in-out;
}

/** Light theme particles on home page */
@keyframes animateParticle {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-2000px);
  }
}

/** styles for public /animation.js */
.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 1s ease,
    transform 1s ease;
}

.animate.show {
  opacity: 1;
  transform: translateY(0);
}

article img {
  padding-top: 20px;
  padding-bottom: 20px;
  display: block;
  margin: 0 auto;
}

/**
 * TWINKLE STARS
 */

#twinkle-star.template {
  @apply absolute -left-full; /* hide offscreen */
}

#twinkle-star.twinkle {
  @apply animate-twinkle; /* defined in tailwind.config */
}

/**
 * Meteors
 */

#meteors .shower {
  @apply absolute inset-0 top-0;
  @apply left-1/2 -translate-x-1/2;
  @apply w-screen aspect-square;
}

#meteors .meteor {
  @apply animate-meteor; /* defined in tailwind.config */
  @apply absolute top-1/2 left-1/2 w-px h-[75vh];
  @apply bg-gradient-to-b from-white to-transparent;
}

#meteors .shower.ur {
  @apply rotate-45;
}

#meteors .shower.dr {
  @apply rotate-135;
}

#meteors .shower.dl {
  @apply rotate-225;
}

#meteors .shower.ul {
  @apply rotate-315;
}

.copy-cnt {
  @apply absolute w-full;
  top: var(--copy-btn-margin);
}
.copy-btn {
  @apply w-[30px] fixed;
  left: calc(100% - var(--copy-btn-margin));
  transform: translateX(-100%);
}

.copy-svg {
  @apply w-full aspect-square text-white opacity-70 hover:opacity-90;
}

.navbar-brand {
  border: 1px solid #ff6601;
  border-right: 2px dotted #ff6601;
  padding: 1px 3px;
  border-radius: 60% 40% 40% 20% / 70% 50% 30% 25%;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/**
 * Pull-to-Reveal Widget Animations
 */

@keyframes pullHint {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

.pull-hint {
  animation: pullHint 2s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.dark .pull-hint {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Custom scrollbar for widget content */
.widget-content::-webkit-scrollbar {
  width: 4px;
}

.widget-content::-webkit-scrollbar-track {
  background: transparent;
}

.widget-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.dark .widget-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}
