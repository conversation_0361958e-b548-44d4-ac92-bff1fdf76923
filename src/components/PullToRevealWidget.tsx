import { createSignal, onMount, onCleanup } from "solid-js";
import { SOCIALS } from "@consts";

interface PullToRevealWidgetProps {
  threshold?: number;
  maxPullDistance?: number;
}

export default function PullToRevealWidget({
  threshold = 120,
  maxPullDistance = 300
}: PullToRevealWidgetProps) {
  const [pullDistance, setPullDistance] = createSignal(0);
  const [isRevealed, setIsRevealed] = createSignal(false);
  const [isDragging, setIsDragging] = createSignal(false);
  const [startY, setStartY] = createSignal(0);
  const [showHint, setShowHint] = createSignal(true);

  let widgetRef: HTMLDivElement | undefined;

  const handleStart = (clientY: number) => {
    setIsDragging(true);
    setStartY(clientY);
    setShowHint(false);
  };

  const handleMove = (clientY: number) => {
    if (!isDragging()) return;

    const deltaY = startY() - clientY;
    const newDistance = Math.max(0, Math.min(deltaY, maxPullDistance));
    setPullDistance(newDistance);
  };

  const handleEnd = () => {
    if (!isDragging()) return;

    setIsDragging(false);

    if (pullDistance() >= threshold) {
      setIsRevealed(true);
      setPullDistance(maxPullDistance);
    } else {
      setIsRevealed(false);
      setPullDistance(0);
    }
  };

  // Mouse events
  const handleMouseDown = (e: MouseEvent) => {
    e.preventDefault();
    handleStart(e.clientY);
  };

  const handleMouseMove = (e: MouseEvent) => {
    e.preventDefault();
    handleMove(e.clientY);
  };

  const handleMouseUp = () => {
    handleEnd();
  };

  // Touch events
  const handleTouchStart = (e: TouchEvent) => {
    e.preventDefault();
    handleStart(e.touches[0].clientY);
  };

  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault();
    handleMove(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    handleEnd();
  };

  // Close widget
  const closeWidget = () => {
    setIsRevealed(false);
    setPullDistance(0);
  };

  onMount(() => {
    // Check if we're in the browser
    if (typeof document === 'undefined') return;

    // Add global event listeners for mouse
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Add global event listeners for touch
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);

    // Hide hint after 5 seconds
    setTimeout(() => {
      setShowHint(false);
    }, 5000);
  });

  onCleanup(() => {
    // Check if we're in the browser
    if (typeof document === 'undefined') return;

    // Remove global event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
  });

  const pullProgress = () => Math.min(pullDistance() / threshold, 1);
  const widgetOpacity = () => 0.6 + (pullProgress() * 0.4);

  return (
    <div class="fixed inset-x-0 bottom-0 z-50 pointer-events-none">
      {/* Debug indicator */}
      <div class="absolute bottom-20 left-4 bg-red-500 text-white px-2 py-1 text-xs rounded pointer-events-auto">
        Widget Loaded
      </div>
      {/* Widget Handle */}
      <div
        ref={widgetRef}
        class="relative mx-auto w-20 h-10 pointer-events-auto cursor-grab active:cursor-grabbing"
        style={{
          transform: `translateY(${-pullDistance()}px)`,
          transition: isDragging() ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        title="Pull up to reveal quick info"
        role="button"
        aria-label="Pull up to reveal quick portfolio information"
        tabindex="0"
      >
        {/* Handle Visual */}
        <div
          class={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-8 bg-black/30 dark:bg-white/30 backdrop-blur-sm rounded-t-xl border border-black/20 dark:border-white/20 flex items-center justify-center ${showHint() && !isDragging() ? 'pull-hint' : ''}`}
          style={{ opacity: widgetOpacity() }}
        >
          <div class="flex flex-col space-y-1">
            <div class="w-8 h-0.5 bg-black/60 dark:bg-white/60 rounded-full"></div>
            <div class="w-6 h-0.5 bg-black/50 dark:bg-white/50 rounded-full mx-auto"></div>
            <div class="w-4 h-0.5 bg-black/40 dark:bg-white/40 rounded-full mx-auto"></div>
          </div>
        </div>

        {/* Pull Indicator */}
        {isDragging() && pullDistance() > 20 && (
          <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-black/60 dark:text-white/60 whitespace-nowrap">
            {pullDistance() >= threshold ? 'Release to open' : 'Pull up to reveal'}
          </div>
        )}
      </div>

      {/* Revealed Content */}
      <div
        class="absolute inset-x-0 bottom-0 bg-white/95 dark:bg-black/95 backdrop-blur-md border-t border-black/10 dark:border-white/10 pointer-events-auto"
        style={{
          transform: `translateY(${100 - (pullDistance() / maxPullDistance) * 100}%)`,
          transition: isDragging() ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          height: `${maxPullDistance}px`
        }}
      >
        <div class="p-6 h-full overflow-y-auto widget-content">
          {/* Close Button */}
          <button
            onClick={closeWidget}
            class="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-black/10 dark:bg-white/10 hover:bg-black/20 dark:hover:bg-white/20 transition-colors"
            aria-label="Close"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Content */}
          <div class="space-y-4 pr-12">
            {/* Quick Bio */}
            <div>
              <h3 class="font-semibold text-black dark:text-white mb-2">Quick Info</h3>
              <p class="text-sm text-black/70 dark:text-white/70">
                Software Engineering student passionate about building user-friendly applications and solving complex problems. Currently pursuing my degree at ENSET Mohammedia.
              </p>
            </div>

            {/* Contact */}
            <div>
              <h3 class="font-semibold text-black dark:text-white mb-2">Get in Touch</h3>
              <div class="space-y-2">
                {SOCIALS.slice(0, 3).map((social) => (
                  <a
                    href={social.HREF}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="flex items-center gap-3 p-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors group"
                  >
                    <div class="w-4 h-4 flex-shrink-0">
                      <svg class="w-full h-full fill-current group-hover:fill-black group-hover:dark:fill-white transition-colors">
                        <use href={`/social.svg#${social.ICON}`} />
                      </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="text-xs text-black/60 dark:text-white/60">{social.NAME}</div>
                      <div class="text-sm text-black dark:text-white truncate">{social.TEXT}</div>
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div>
              <h3 class="font-semibold text-black dark:text-white mb-2">Quick Actions</h3>
              <div class="flex gap-2">
                <a
                  href="/resume.pdf"
                  target="_blank"
                  class="flex-1 py-2 px-3 text-xs bg-black dark:bg-white text-white dark:text-black rounded-lg hover:opacity-75 transition-opacity text-center"
                >
                  Download Resume
                </a>
                <a
                  href="/projects"
                  class="flex-1 py-2 px-3 text-xs border border-black/25 dark:border-white/25 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors text-center"
                >
                  View Projects
                </a>
              </div>
            </div>

            {/* Status */}
            <div class="pt-2 border-t border-black/10 dark:border-white/10">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-xs text-black/60 dark:text-white/60">Available for opportunities</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
